// Mock for swiper/react
module.exports = {
    Swiper: ({ children, ...props }) => {
        return {
            $$typeof: Symbol.for('react.element'),
            type: 'div',
            props: {
                'data-testid': 'mock-swiper',
                ...props,
                children
            }
        };
    },
    SwiperSlide: ({ children, ...props }) => {
        return {
            $$typeof: Symbol.for('react.element'),
            type: 'div',
            props: {
                'data-testid': 'mock-swiper-slide',
                ...props,
                children
            }
        };
    }
};
