import apiUrls from '@constants/apiUrls';
import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@utils/axiosBaseQuery';

export const goalProgressListApi = createApi({
    reducerPath: 'goalProgressListApi',
    baseQuery: axiosBaseQuery({
        baseUrl: apiUrls.goalProgressList
    }),
    tagTypes: ['goalProgressList'],
    endpoints: builder => ({
        getGoalProgressList: builder.query({
            query: () => ({
                url: '/'
            }),
            providesTags: ['goalProgressList']
        })
    })
});

export const { useGetGoalProgressListQuery } = goalProgressListApi;
