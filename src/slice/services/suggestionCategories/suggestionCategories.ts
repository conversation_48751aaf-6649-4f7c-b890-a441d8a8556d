import apiUrls from '@constants/apiUrls';
import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@utils/axiosBaseQuery';
import { createUrlWithParams } from '@utils/createUrlWithParams';
import { createUrlWithParamsTypes } from '@utils/types/types';
import { SuggestionCategory } from './types';

export const suggestionCategoryAPI = createApi({
    reducerPath: 'SuggestionCategory',
    baseQuery: axiosBaseQuery({
        baseUrl: apiUrls.suggestionCategories
    }),
    tagTypes: ['suggestionCategories'],
    endpoints: builder => ({
        getSuggestionCategories: builder.query<SuggestionCategory[], createUrlWithParamsTypes>({
            query: payload => ({
                url: createUrlWithParams(payload)
            }),
            providesTags: ['suggestionCategories']
        })
    })
});

export const { useGetSuggestionCategoriesQuery } = suggestionCategoryAPI;
