import apiUrls from '@constants/apiUrls';
import { GoalsResponse } from '@pages/Goals/types';
import { createApi } from '@reduxjs/toolkit/query/react';
import { axiosBaseQuery } from '@utils/axiosBaseQuery';
import { createUrlWithParams } from '@utils/createUrlWithParams';
import { createUrlWithParamsTypes } from '@utils/types/types';

export const goalsApi = createApi({
    reducerPath: 'goals',
    baseQuery: axiosBaseQuery({
        baseUrl: apiUrls.goals
    }),
    tagTypes: ['goals'],
    endpoints: builder => ({
        getMyGoalList: builder.query<GoalsResponse, createUrlWithParamsTypes>({
            query: payload => ({
                url: createUrlWithParams(payload)
            }),
            providesTags: ['goals']
        }),
        addGoal: builder.mutation({
            query: newGoal => ({
                url: '',
                method: 'POST',
                data: newGoal
            }),
            invalidatesTags: ['goals']
        }),
        updateGoal: builder.mutation({
            query: payload => ({
                url: `/${payload.goalId}`,
                method: 'PATCH',
                data: payload
            }),
            invalidatesTags: ['goals']
        })
    })
});

export const { useGetMyGoalListQuery, useAddGoalMutation, useUpdateGoalMutation } = goalsApi;
