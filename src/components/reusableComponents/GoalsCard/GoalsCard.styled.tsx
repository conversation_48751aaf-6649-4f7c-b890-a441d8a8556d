import { Text } from '@medly-components/core';
import styled from 'styled-components';

export const GoalsCardItem = styled.div<{ backgroundColor: string }>`
    background-color: ${({ backgroundColor }) => `${backgroundColor}20`};
    display: flex;
    position: relative;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    border-radius: 0.5rem;
    width: 100%;
    height: 15rem;
    padding: 0;
    flex: 1 1 0;
`;

export const CountDisplay = styled(Text)<{ color: string }>`
    font-size: 2.5rem;
    font-weight: bold;
    color: ${({ color }) => color};
    line-height: 1;
    margin-bottom: 0.25rem;

    @media (max-width: 768px) {
        font-size: 2rem;
    }
`;

export const GoalLabel = styled(Text).attrs({
    textVariant: 'h4',
    textWeight: 'Regular',
    textAlign: 'center'
})`
    color: ${({ theme }) => theme.contentColor};
`;
