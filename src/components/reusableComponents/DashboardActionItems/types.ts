import { GoalData } from '@pages/Goals/types';

export interface GoalProgressDataAndGoalTypeData {
    id: number;
    label: string;
    count: number;
}

export interface ActionItemsResponse {
    goalType: GoalProgressDataAndGoalTypeData[];
    goalProgress: GoalProgressDataAndGoalTypeData[];
    goals?: GoalData[];
    totalGoals?: number;
}

export interface PieChartComponentProps {
    data: GoalProgressDataAndGoalTypeData[];
    customTooltip: React.FC<any>;
}

export interface PieChartData {
    id: string;
    label: string;
    value: number;
    color: string;
}

export interface GoalsOverviewProps {
    data: GoalProgressDataAndGoalTypeData[];
}

export interface DashboardGoalTypesPieChartProps {
    customTooltip: React.FC<any>;
}
