import { ColumnActionText } from '@common';
import { TableColumnConfig } from '@medly-components/core';
import { useAppSelector } from '@slice';
import { useGoalModalContext } from '@pages/Goals/GoalModal/GoalModalContext';
import { GoalData } from '@pages/Goals/types';

export const GoalActionFormatter: TableColumnConfig['component'] = ({ rowData }) => {
    const { openModal } = useGoalModalContext();
    const currentUser = useAppSelector(state => state.user);

    const isSelfGoal = rowData?.createdBy === rowData?.assignedTo;
    const isCreator = rowData?.createdBy === Number(currentUser.id);
    const isOwner = rowData?.assignedTo === Number(currentUser.id);

    // Show "View" button if user is neither creator nor owner
    const isViewOnly = (isCreator && rowData?.progressId !== 1 && !isSelfGoal) || (!isCreator && !isOwner);
    const buttonText = isViewOnly ? 'View' : 'Edit';

    const handleClick = () => {
        openModal(rowData as GoalData, buttonText, isSelfGoal);
    };

    return <ColumnActionText onClick={handleClick}>{buttonText}</ColumnActionText>;
};
