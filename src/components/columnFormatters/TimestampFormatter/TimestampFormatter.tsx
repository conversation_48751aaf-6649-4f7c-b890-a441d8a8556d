import { TableColumnConfig, Text } from '@medly-components/core';

const formatTimestamp = (timestamp: number | string) => {
    // Convert timestamp to Date object
    const date = new Date(timestamp);

    // Check if date is valid
    if (isNaN(date.getTime())) {
        return 'Invalid date';
    }

    // Format date and time
    const dateStr = date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });

    const timeStr = date.toLocaleTimeString('en-US', {
        hour: '2-digit',
        minute: '2-digit',
        hour12: false
    });

    return `${dateStr} ${timeStr}`;
};

export const TimestampFormatter: TableColumnConfig['component'] = ({ rowData }) => {
    return <Text>{rowData?.lastActive ? formatTimestamp(rowData.lastActive) : 'No recent activity'}</Text>;
};
