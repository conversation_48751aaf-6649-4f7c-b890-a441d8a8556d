// Jest Snapshot v1, https://goo.gl/fbAQLP

exports[`Head<PERSON> should render properly 1`] = `
<div>
  <header
    class="sc-16ik3hp-0 gjqjjH"
    id="click-outside-dropdown"
  >
    <div
      class="sc-16ik3hp-1 hETOWa"
    >
      <ol
        class="sc-1frtvp3-0 iXOpqi"
      >
        <li
          class="sc-qumdxm-0 jWCLpb sc-16ik3hp-5 hXnkuL"
          textcolor="[object Object]"
        >
          <h4
            class="sc-1ntgl3l-0 iEGCWU"
            textalign="initial"
            textcolor="inherit"
            textvariant="h4"
          />
        </li>
      </ol>
      <h3
        class="sc-1ntgl3l-0 kcLBOi sc-16ik3hp-3 jgCRvi"
        textalign="initial"
        textcolor="inherit"
        textvariant="h3"
        textweight="Medium"
      >
        Welcome, 
        ABC
      </h3>
    </div>
    <div
      class="sc-16ik3hp-4 geYifm"
    >
      <div
        class="sc-1hcsztp-0 hyshfb sc-tkiih6-13 cecHDR"
        id="review cycle-wrapper"
        minwidth="28rem"
        variant="outlined"
      >
        <div
          class="sc-17d4s39-0 fpSDTg"
          id="review cycle-input-wrapper"
          minwidth="28rem"
        >
          <div
            class="sc-19px6zp-0 BrSGZ"
            filled="[object Object]"
            fusion="[object Object]"
            minrows="1"
            outlined="[object Object]"
            textvariant="[object Object]"
            variant="outlined"
          >
            <div
              class="sc-lhr232-0 btNOKw"
              variant="outlined"
            >
              <input
                aria-describedby="review cycle-helper-text"
                autocomplete="off"
                class="sc-hb99wz-0 jaEbhp"
                data-tooltip-id="review cycle-input"
                errortext=""
                id="review cycle-input"
                inputmode="none"
                inputsize="M"
                placeholder="Select Review Cycle"
                type="text"
                value=""
                variant="outlined"
              />
              <label
                class="sc-1du51u0-0 eMZrST"
                for="review cycle-input"
                inputwidth="0"
                variant="outlined"
              >
                Review cycle
              </label>
            </div>
            <svg
              class="sc-1pf3x8a-0 klDMsE sc-dynlvq-0 bdmviR"
              fill="none"
              height="1em"
              viewBox="0 0 24 24"
              width="1em"
              xmlns="http://www.w3.org/2000/svg"
            >
              <path
                d="M12.792 14.97a1 1 0 0 1-1.584 0l-3.165-4.11a1 1 0 0 1 .793-1.61h6.328a1 1 0 0 1 .793 1.61l-3.165 4.11z"
                fill="#000"
              />
            </svg>
          </div>
          
        </div>
      </div>
      
      <div
        class="sc-1298oxm-0 dQDoHN"
      >
        <div
          class="sc-1298oxm-3 kwZxBE"
        >
          <div
            class="sc-1dzehji-0 hjLOhC"
            defaults="[object Object]"
          >
            <strong
              class="sc-1ntgl3l-0 fzLUlA"
              textalign="initial"
              textcolor="inherit"
              textweight="Strong"
            >
              A
            </strong>
            <strong
              class="sc-1ntgl3l-0 fzLUlA"
              textalign="initial"
              textcolor="inherit"
              textweight="Strong"
            >
              D
            </strong>
          </div>
        </div>
        <div
          class="sc-1298oxm-2 ekJHDu inactive"
        >
          <ul
            class="sc-1298oxm-1 jVMYcc"
          >
            <li
              class="sc-1298oxm-4 giVItn"
            >
              <div
                class="sc-1dzehji-0 hjLOhC"
                defaults="[object Object]"
                style="height: 100%; width: 100%;"
              >
                <span
                  class="sc-1ntgl3l-0 hGagSU"
                  style="margin: 2rem 2rem; font-size: 3rem; font-weight: 1rem;"
                  textalign="initial"
                  textcolor="inherit"
                >
                  A
                  D
                </span>
              </div>
            </li>
            <div
              bg="white"
              borderradius="2"
              class="sc-12ecrgm-0 dAEUnm sc-1298oxm-8 Hlhle"
              color="black"
              display="flex"
              font-variant="body2"
              px="4"
              py="4"
            >
              <h4
                class="sc-1ntgl3l-0 igfIhV"
                textalign="center"
                textcolor="inherit"
                textvariant="h4"
              >
                ABC
                 
                DEF
                 
                (SR0022)
              </h4>
              <h5
                class="sc-1ntgl3l-0 dBSnDZ sc-1298oxm-7 kNgBfF"
                textalign="center"
                textcolor="inherit"
                textvariant="h5"
                textweight="Medium"
              >
                Team: 
                <span>
                  BA
                </span>
              </h5>
              <h5
                class="sc-1ntgl3l-0 dBSnDZ sc-1298oxm-7 kNgBfF"
                textalign="center"
                textcolor="inherit"
                textvariant="h5"
                textweight="Medium"
              >
                Designation: 
                <span>
                  BA - Traineee
                </span>
              </h5>
              <h5
                class="sc-1ntgl3l-0 dBSnDZ sc-1298oxm-7 kNgBfF"
                textalign="center"
                textcolor="inherit"
                textvariant="h5"
                textweight="Medium"
              >
                Role: 
                <span>
                  Manager
                </span>
              </h5>
              <li
                class="sc-1298oxm-5 jElgbs"
              >
                <div
                  class="sc-1ep6kh8-0 hrIcnd"
                  id="medly-popover-wrapper"
                >
                  <h5
                    class="sc-1ntgl3l-0 dBSnDZ sc-1298oxm-7 kNgBfF"
                    textalign="center"
                    textcolor="inherit"
                    textvariant="h5"
                    textweight="Medium"
                  >
                    <EMAIL>
                  </h5>
                </div>
              </li>
              <h5
                class="sc-1ntgl3l-0 dBSnDZ sc-1298oxm-7 kNgBfF"
                textalign="center"
                textcolor="inherit"
                textvariant="h5"
                textweight="Medium"
              >
                +917060753645
              </h5>
            </div>
            <li
              class="sc-1298oxm-6 bKQgdL"
            >
              <button
                class="sc-1k2druj-0 dsEKPA"
                edges="square"
                type="button"
                variant="outlined"
              >
                <span
                  class="sc-1ntgl3l-0 eOsHHD"
                  textalign="initial"
                  textcolor="inherit"
                  textvariant="button1"
                >
                  Sign Out
                </span>
              </button>
            </li>
          </ul>
        </div>
      </div>
    </div>
  </header>
</div>
`;
