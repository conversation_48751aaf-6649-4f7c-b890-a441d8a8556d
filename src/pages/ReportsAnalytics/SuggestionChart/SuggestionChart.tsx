import { StyledEmptyText } from '@common';
import { Loader } from '@components/reusableComponents';
import { ChartTitle, TooltipColor, TooltipContainer, ChartWrapper } from '@components/reusableComponents/Charts/Chart.styled';
import { Text } from '@medly-components/core';
import { Text as ChartLabel } from '../ChartAnalytics/ChartAnalytics.styled';
import { ResponsiveBar } from '@nivo/bar';
import { ResponsivePieCanvas } from '@nivo/pie';
import customColors from '@theme/core/colors';
import { FC, memo, useCallback, useMemo } from 'react';
import { suggestionProgressCategories, suggestionProgressColors } from './constants';
import {
    BarChartWrapper,
    CategoriesGrid,
    ChartContainer,
    Color,
    ColorScheme,
    EmptyStateContainer,
    SuggestionCategoryWrapper,
    SuggestionProgressWrapper,
    TileTitle,
    TypeText
} from './SuggestionChart.styled';
import { Props, SuggestionCategoryItem, SuggestionProgressDataItem, SuggestionProgressLabel } from './types';
import { useSuggestionChart } from './useSuggestionChart';

// Function to calculate percentage for Suggestion Progress Pie Chart
export function calculatePercentageForProgressPie(data: SuggestionProgressDataItem[]): SuggestionCategoryItem[] {
    const total = data.reduce((sum, item) => sum + item.count, 0);
    return data.map((item, index) => ({
        id: index + 1,
        label: item.label as SuggestionProgressLabel,
        count: item.count,
        percentage: total === 0 ? 0 : Number(((item.count / total) * 100).toFixed(1))
    }));
}

export const SuggestionChart: FC<Props> = memo(({ reviewCycleId }) => {
    const { suggestionCategory, suggestionProgress, isLoading } = useSuggestionChart(reviewCycleId);

    const hasProgressData = suggestionProgress && suggestionProgress.length > 0 && suggestionProgress.some(item => item.count > 0);

    const progressTooltip = useCallback(
        ({ value }: { value: number }) => <TooltipContainer>{`${value} ${value > 1 ? 'Suggestions' : 'Suggestion'}`}</TooltipContainer>,
        []
    );

    const renderProgressChart = () => {
        if (isLoading) {
            return <Loader />;
        }

        if (!hasProgressData) {
            return (
                <EmptyStateContainer>
                    <StyledEmptyText>
                        {reviewCycleId
                            ? 'No Suggestions found for the selected review cycle.'
                            : 'Please select a review cycle to view details.'}
                    </StyledEmptyText>
                </EmptyStateContainer>
            );
        }

        return (
            <BarChartWrapper>
                <ResponsiveBar
                    data={suggestionCategory}
                    keys={['count']}
                    indexBy="label"
                    margin={{ top: 20, right: 0, bottom: 60, left: 0 }}
                    padding={0.65}
                    enableLabel={false}
                    axisLeft={null}
                    axisBottom={{
                        tickSize: 0,
                        renderTick(props) {
                            const { x, y, value } = props;
                            return (
                                <g transform={`translate(${x},${y})`}>
                                    <foreignObject x={'-40'} y="4" dy={16} width="80" height="60">
                                        <ChartLabel>{value}</ChartLabel>
                                    </foreignObject>
                                </g>
                            );
                        }
                    }}
                    colors={customColors.appreciation}
                    enableGridY={false}
                    tooltip={progressTooltip}
                />
            </BarChartWrapper>
        );
    };

    return (
        <ChartContainer>
            <SuggestionProgressWrapper>
                <TileTitle textVariant="h4">Suggestions by Progress</TileTitle>
                {isLoading ? (
                    <Loader />
                ) : (
                    <SuggestionProgressPie reviewCycleId={reviewCycleId} data={calculatePercentageForProgressPie(suggestionProgress)} />
                )}
                <CategoriesGrid>
                    {suggestionProgressCategories.map((node: string) => (
                        <ColorScheme key={node}>
                            <Color bgColor={suggestionProgressColors[node as keyof typeof suggestionProgressColors]} />
                            <TypeText>{node}</TypeText>
                        </ColorScheme>
                    ))}
                </CategoriesGrid>
            </SuggestionProgressWrapper>
            <SuggestionCategoryWrapper>
                <TileTitle textVariant="h4">Suggestions by Category</TileTitle>
                <div style={{ width: '100%' }}>{renderProgressChart()}</div>
            </SuggestionCategoryWrapper>
        </ChartContainer>
    );
});

SuggestionChart.displayName = 'SuggestionChart';

export const SuggestionProgressPie = ({ reviewCycleId, data }: { reviewCycleId: string; data: SuggestionCategoryItem[] }) => {
    const hasData = data && data.length > 0 && data.some(item => item.count > 0);

    const pieChartData = useMemo(
        () =>
            data
                .map(item => ({
                    id: item.label,
                    label: item.label,
                    value: item.count,
                    percentage: item.percentage,
                    color: suggestionProgressColors[item.label as keyof typeof suggestionProgressColors]
                }))
                ?.filter(item => item.value > 0),
        [data]
    );

    const customTooltip = useCallback(({ datum }: { datum: any }) => {
        return (
            <TooltipContainer>
                <TooltipColor bgcolor={datum.color} />
                <Text textVariant="body1">{datum.id}:</Text>
                <Text textVariant="h5" textWeight="Strong">
                    {datum.data.percentage}%
                </Text>
            </TooltipContainer>
        );
    }, []);

    if (!hasData) {
        return (
            <EmptyStateContainer>
                <StyledEmptyText marginTop="4.5rem">
                    {reviewCycleId
                        ? 'No Suggestions found for the selected review cycle.'
                        : 'Please select a review cycle to view details.'}
                </StyledEmptyText>
            </EmptyStateContainer>
        );
    }

    return (
        <ChartWrapper>
            <ChartTitle>Overview</ChartTitle>
            <ResponsivePieCanvas
                data={pieChartData}
                margin={{ top: 30, right: 30, bottom: 30, left: 30 }}
                innerRadius={0.65}
                padAngle={0.7}
                cornerRadius={1}
                activeOuterRadiusOffset={4}
                borderColor={{
                    from: 'color',
                    modifiers: [['darker', 0.1]]
                }}
                colors={pieChartData.map(item => item.color)}
                enableArcLinkLabels={false}
                enableArcLabels={true}
                arcLabelsSkipAngle={8}
                arcLabelsTextColor="#333333"
                arcLabel={d => String(d.data.value)}
                isInteractive={true}
                tooltip={customTooltip}
                theme={{
                    labels: {
                        text: {
                            fontSize: 14
                        }
                    }
                }}
            />
        </ChartWrapper>
    );
};
