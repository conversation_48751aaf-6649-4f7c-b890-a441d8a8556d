import { useSelectEmployee } from '@components';
import { routeConstants } from '@constants';
import { ALL_OPTION } from '@constants/data';
import { Option } from '@medly-components/core/dist/es/components/SearchBox/types';
import { useAppSelector } from '@slice';
import { useGetGoalProgressListQuery, useGetGoalTypesQuery, useGetMyGoalListQuery } from '@slice/services';
import { SetStateAction, useCallback, useMemo, useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { GoalProgress, GoalType, TabTypes } from './types';

export const useGoals = () => {
    const [activeTab, setActiveTab] = useState<TabTypes>('myGoals'),
        // setting the default as -99 to indicate no filter on backend
        [goalStatusFilter, setGoalStatusFilter] = useState(ALL_OPTION.value),
        [goalTypeFilter, setGoalTypeFilter] = useState(ALL_OPTION.value),
        navigateTo = useNavigate(),
        { ActivePage } = useParams(),
        // declaring the goalOwner as -99, so that default "Me" value kicks in
        [goalOwner, setGoalOwner] = useState([-99]);

    const { modulePermission, organisationId } = useAppSelector(state => state.user);

    const teamGoalsAccess = modulePermission?.find(module => module.moduleName === 'Team Goals')?.view ?? false;

    const { employeeList: filteredEmployeeList } = useSelectEmployee(true);
    const employeeList = useMemo(() => filteredEmployeeList(), [filteredEmployeeList]);

    const reviewCycleList = useAppSelector(state => state.reviewCycleFilter.reviewCycleList);

    const { data: myGoalsData, isFetching: isMyGoalFetching } = useGetMyGoalListQuery(
        {
            path: '',
            params: [
                { name: 'organisationId', value: organisationId },
                { name: 'reviewCycleId', value: reviewCycleList?.join(',') || -99 },
                { name: 'progressId', value: goalStatusFilter },
                { name: 'typeId', value: goalTypeFilter },
                { name: 'page', value: ActivePage ?? 1 },
                { name: 'limit', value: 10 }
            ]
        },
        { refetchOnMountOrArgChange: true }
    );

    const { data: teamGoalsData, isFetching: isTeamGoalFetching } = useGetMyGoalListQuery(
        {
            path: 'team/',
            params: [
                { name: 'organisationId', value: organisationId },
                { name: 'reviewCycleId', value: reviewCycleList?.join(',') || -99 },
                { name: 'progressId', value: goalStatusFilter },
                { name: 'assignedTo', value: goalOwner.join(',') || -99 },
                { name: 'typeId', value: goalTypeFilter },
                { name: 'page', value: ActivePage ?? 1 },
                { name: 'limit', value: 10 }
            ]
        },
        { refetchOnMountOrArgChange: true, skip: !teamGoalsAccess }
    );

    const { data: goalTypesData } = useGetGoalTypesQuery({
        path: '',
        params: []
    });

    const goalTypeOptions = useMemo(() => {
        const baseOptions = (goalTypesData ?? []).map((item: GoalType) => ({
            value: item.typeId,
            label: item.typeName
        }));

        return {
            withAll: [ALL_OPTION, ...baseOptions],
            withoutAll: baseOptions
        };
    }, [goalTypesData]);

    const { data: goalProgressData } = useGetGoalProgressListQuery({
        path: '',
        params: []
    });

    const goalStatusOptions = useMemo(() => {
        const baseOptions: Option[] = (goalProgressData ?? []).map((item: GoalProgress) => ({
            value: item.progressId,
            label: item.progressName
        }));

        return {
            withAll: [ALL_OPTION, ...baseOptions],
            withoutAll: baseOptions
        };
    }, [goalProgressData]);

    const handleTabChange = useCallback(
        (id: TabTypes) => {
            setActiveTab(id);
            navigateTo(`${routeConstants.goals}/1`);

            // resetting the filters on tab change
            setGoalStatusFilter(ALL_OPTION.value);
            setGoalTypeFilter(ALL_OPTION.value);
            setGoalOwner([-99]);
        },
        [navigateTo]
    );

    const handlePageChange = (page: number) => {
        navigateTo(`${routeConstants.goals}/${page}`);
    };

    const handleGoalStatusChange = useCallback(
        (value: number) => {
            setGoalStatusFilter(value);
            navigateTo(`${routeConstants.goals}/1`);
        },
        [navigateTo]
    );

    const handleGoalTypeChange = useCallback(
        (value: number) => {
            setGoalTypeFilter(value);
            navigateTo(`${routeConstants.goals}/1`);
        },
        [navigateTo]
    );

    const handleSelectOwner = useCallback(
        (value: SetStateAction<number[]>) => {
            setGoalOwner(value);
            navigateTo(`${routeConstants.goals}/1`);
        },
        [navigateTo]
    );

    const goalsData = activeTab === 'myGoals' ? myGoalsData : teamGoalsData;

    return {
        activeTab,
        handleTabChange,
        goalsData: goalsData?.goals || [],
        page: ActivePage,
        handlePageChange,
        totalGoalsDataCount: goalsData?.totalGoals ?? 0,
        goalsDataIsLoading: activeTab === 'myGoals' ? isMyGoalFetching : isTeamGoalFetching,
        goalStatusFilter,
        goalTypeFilter,
        goalOwner,
        handleGoalStatusChange,
        handleGoalTypeChange,
        handleSelectOwner,
        teamGoalsAccess,
        employeeList,
        goalTypeOptions,
        goalStatusOptions
    };
};
