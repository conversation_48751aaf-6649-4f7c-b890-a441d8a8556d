import { StyledHeading, StyledHTMLText, StyledText } from '../Goals.styled';
import { GoalData } from '../types';

const ViewGoalContent = ({ currentGoal }: { currentGoal: GoalData | null }) => {
    return (
        <>
            <StyledHeading textWeight="Medium">
                Type: <StyledText>{currentGoal?.typeName}</StyledText>
            </StyledHeading>
            <StyledHeading textWeight="Medium">
                Owner: <StyledText>{`${currentGoal?.assignedToName} (${currentGoal?.assignedToEmployeeId})`}</StyledText>
            </StyledHeading>
            <StyledHeading textWeight="Medium">
                Created By: <StyledText>{`${currentGoal?.createdByName} (${currentGoal?.createdByEmployeeId})`}</StyledText>
            </StyledHeading>
            <StyledHeading textWeight="Medium">
                Progress: <StyledText>{currentGoal?.progressName}</StyledText>
            </StyledHeading>
            <StyledHeading textWeight="Medium">
                Deadline: <StyledText>{currentGoal?.targetDate && new Date(currentGoal.targetDate).toLocaleDateString('en-GB')}</StyledText>
            </StyledHeading>
            <StyledHeading textWeight="Medium">
                Goal: <StyledHTMLText>{currentGoal?.description}</StyledHTMLText>
            </StyledHeading>
        </>
    );
};

export default ViewGoalContent;
