import { createContext, ReactNode, useCallback, useContext, useEffect, useMemo, useRef, useState } from 'react';
import { GoalData, ModalAction } from '../types';

interface GoalModalContextType {
    isOpen: boolean;
    currentGoal: GoalData | null;
    action: ModalAction;
    isSelfGoal: boolean;
    openModal: (goal?: GoalData | null, action?: ModalAction, isSelfGoal?: boolean) => void;
    closeModal: () => void;
}

const GoalModalContext = createContext<GoalModalContextType | undefined>(undefined);

interface GoalModalProviderProps {
    children: ReactNode;
}

export const GoalModalProvider = ({ children }: GoalModalProviderProps) => {
    const [isOpen, setIsOpen] = useState(false);
    const [currentGoal, setCurrentGoal] = useState<GoalData | null>(null);
    const [action, setAction] = useState<ModalAction>('Add');
    const [isSelfGoal, setIsSelfGoal] = useState(false);
    const timeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

    const openModal = useCallback((goal: GoalData | null = null, modalAction: ModalAction = 'Add', access = false) => {
        // Clear any existing timeout
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
            timeoutRef.current = null;
        }

        setCurrentGoal(goal);
        setAction(modalAction);
        setIsSelfGoal(access);
        setIsOpen(true);
    }, []);

    const closeModal = useCallback(() => {
        setIsOpen(false);
        setCurrentGoal(null);

        // Clear any existing timeout before setting a new one
        if (timeoutRef.current) {
            clearTimeout(timeoutRef.current);
        }

        timeoutRef.current = setTimeout(() => {
            setAction('Add');
            setIsSelfGoal(false);
            timeoutRef.current = null;
        }, 200);
    }, []);

    // Cleanup timeout on unmount
    useEffect(() => {
        return () => {
            if (timeoutRef.current) {
                clearTimeout(timeoutRef.current);
            }
        };
    }, []);

    const contextValue: GoalModalContextType = useMemo(
        () => ({
            isOpen,
            currentGoal,
            action,
            isSelfGoal,
            openModal,
            closeModal
        }),
        [isOpen, currentGoal, action, isSelfGoal, openModal, closeModal]
    );

    return <GoalModalContext.Provider value={contextValue}>{children}</GoalModalContext.Provider>;
};

export const useGoalModalContext = (): GoalModalContextType => {
    const context = useContext(GoalModalContext);
    if (!context) {
        throw new Error('useGoalModalContext must be used within a GoalModalProvider');
    }
    return context;
};
